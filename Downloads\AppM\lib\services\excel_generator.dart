import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart';
import 'package:open_file/open_file.dart';
import '../models/auditoria_model.dart';
import '../utils/constants.dart';
import 'photo_service.dart';

class ExcelGenerator {
  // Generar archivo Excel con datos y fotos
  static Future<String?> generarExcel(AuditoriaModel auditoria) async {
    try {
      debugPrint('Iniciando generación de Excel...');

      // Crear nuevo workbook
      final Workbook workbook = Workbook();
      debugPrint('Workbook creado');

      final Worksheet worksheet = workbook.worksheets[0];
      debugPrint('Worksheet obtenido');

      // Configurar nombre de la hoja
      worksheet.name = 'Auditoría Movistar';
      debugPrint('Nombre de hoja configurado');

      // Configurar encabezados y datos generales
      await _configurarEncabezados(worksheet, auditoria);
      debugPrint('Encabezados configurados');

      // Agregar datos de ítems
      await _agregarItemsYFotos(worksheet, auditoria);
      debugPrint('Ítems agregados');

      // Guardar archivo
      final String? rutaArchivo = await _guardarArchivo(workbook, auditoria);
      debugPrint('Archivo guardado: $rutaArchivo');

      // NO liberar recursos - esto causa el error de lista inmutable
      // workbook.dispose();
      debugPrint('Saltando dispose para evitar error de lista inmutable');

      return rutaArchivo;
    } catch (e) {
      debugPrint('Error al generar Excel: $e');
      debugPrint('Stack trace: ${StackTrace.current}');
      return null;
    }
  }

  // Configurar encabezados y datos generales
  static Future<void> _configurarEncabezados(
    Worksheet worksheet, 
    AuditoriaModel auditoria
  ) async {
    // Título principal
    worksheet.getRangeByName('A1:F1').merge();
    worksheet.getRangeByName('A1').setText('AUDITORÍA TÉCNICA MOVISTAR');
    worksheet.getRangeByName('A1').cellStyle.fontSize = 16;
    worksheet.getRangeByName('A1').cellStyle.bold = true;
    worksheet.getRangeByName('A1').cellStyle.hAlign = HAlignType.center;
    worksheet.getRangeByName('A1').cellStyle.backColor = '#0066CC';
    worksheet.getRangeByName('A1').cellStyle.fontColor = '#FFFFFF';

    // Datos generales
    int fila = 3;
    final Map<String, String> datosGenerales = {
      'Número de Orden:': auditoria.numeroOrden,
      'Nombre del Técnico:': auditoria.nombreTecnico,
      'Cédula del Técnico:': auditoria.cedulaTecnico,
      'Nombre del Auditor:': auditoria.nombreAuditor,
      'Ciudad:': auditoria.ciudad,
      'Empresa Contratista:': auditoria.empresaContratista,
      'Tipo de Solicitud:': auditoria.tipoSolicitud,
      'Dirección:': auditoria.direccion,
      'Fecha de Auditoría:': auditoria.fechaAuditoria,
      'Observaciones:': auditoria.observaciones,
    };

    for (MapEntry<String, String> entry in datosGenerales.entries) {
      worksheet.getRangeByName('A$fila').setText(entry.key);
      worksheet.getRangeByName('A$fila').cellStyle.bold = true;
      worksheet.getRangeByName('B$fila').setText(entry.value);
      fila++;
    }

    // Encabezados de la tabla de ítems
    fila += 2;
    final List<String> encabezados = [
      'Ítem',
      'Fotos Normales (1-3)',
      'Tiene Hallazgo',
      'Fotos Hallazgo (1-3)',
      'Estado'
    ];

    for (int i = 0; i < encabezados.length; i++) {
      final String celda = String.fromCharCode(65 + i) + fila.toString();
      worksheet.getRangeByName(celda).setText(encabezados[i]);
      worksheet.getRangeByName(celda).cellStyle.bold = true;
      worksheet.getRangeByName(celda).cellStyle.backColor = '#4A90E2';
      worksheet.getRangeByName(celda).cellStyle.fontColor = '#FFFFFF';
    }
  }

  // Agregar ítems y fotos al Excel
  static Future<void> _agregarItemsYFotos(
    Worksheet worksheet,
    AuditoriaModel auditoria
  ) async {
    int filaInicial = 12; // Después de los encabezados

    for (int i = 0; i < AuditoriaModel.itemsAuditoria.length; i++) {
      final String item = AuditoriaModel.itemsAuditoria[i];
      final int filaActual = filaInicial + i;

      // Configurar altura de fila para las fotos
      worksheet.setRowHeightInPixels(filaActual, 120);

      // Nombre del ítem
      worksheet.getRangeByName('A$filaActual').setText(item);

      // Fotos normales
      final List<File> fotosNormales = auditoria.fotosNormales[item] ?? [];
      if (fotosNormales.isNotEmpty) {
        // Insertar primera foto en la celda principal
        await _insertarFoto(worksheet, fotosNormales[0], 'B$filaActual');

        // Agregar texto con el número total de fotos
        if (fotosNormales.length > 1) {
          worksheet.getRangeByName('B${filaActual + 1}').setText('Total: ${fotosNormales.length} fotos');
        }
      } else {
        worksheet.getRangeByName('B$filaActual').setText('Sin fotos');
      }

      // Tiene hallazgo
      final bool tieneHallazgo = auditoria.tieneHallazgo[item] ?? false;
      worksheet.getRangeByName('C$filaActual').setText(tieneHallazgo ? 'SÍ' : 'NO');

      // Fotos de hallazgo
      if (tieneHallazgo) {
        final List<File> fotosHallazgos = auditoria.fotosHallazgos[item] ?? [];
        if (fotosHallazgos.isNotEmpty) {
          // Insertar primera foto de hallazgo
          await _insertarFoto(worksheet, fotosHallazgos[0], 'D$filaActual');

          // Agregar texto con el número total de fotos de hallazgo
          if (fotosHallazgos.length > 1) {
            worksheet.getRangeByName('D${filaActual + 1}').setText('Total: ${fotosHallazgos.length} fotos');
          }
        } else {
          worksheet.getRangeByName('D$filaActual').setText('Pendiente');
        }
      } else {
        worksheet.getRangeByName('D$filaActual').setText('N/A');
      }

      // Estado
      String estado = 'Completo';
      if (fotosNormales.isEmpty) {
        estado = 'Faltan fotos normales';
      } else if (tieneHallazgo && (auditoria.fotosHallazgos[item]?.isEmpty ?? true)) {
        estado = 'Faltan fotos de hallazgo';
      }
      worksheet.getRangeByName('E$filaActual').setText(estado);
    }

    // Ajustar ancho de columnas
    worksheet.autoFitColumn(1); // Columna A
    worksheet.setColumnWidthInPixels(2, 150); // Columna B (foto normal)
    worksheet.autoFitColumn(3); // Columna C
    worksheet.setColumnWidthInPixels(4, 150); // Columna D (foto hallazgo)
    worksheet.autoFitColumn(5); // Columna E
  }

  // Insertar foto en una celda específica
  static Future<void> _insertarFoto(
    Worksheet worksheet,
    File archivoFoto,
    String celda
  ) async {
    try {
      final Uint8List? bytesImagen = await PhotoService.obtenerBytesImagen(archivoFoto);
      if (bytesImagen != null) {
        // Obtener coordenadas de la celda
        final Range range = worksheet.getRangeByName(celda);
        final int fila = range.row;
        final int columna = range.column;

        // Crear una copia modificable de los bytes
        final List<int> bytesList = List<int>.from(bytesImagen);

        // Agregar imagen al worksheet
        final Picture picture = worksheet.pictures.addBase64(fila, columna,
            base64.encode(Uint8List.fromList(bytesList)));

        // Configurar tamaño de la imagen
        picture.width = 140;
        picture.height = 100;
      }
    } catch (e) {
      debugPrint('Error al insertar foto en $celda: $e');
      worksheet.getRangeByName(celda).setText('Error al cargar foto');
    }
  }

  // Guardar archivo Excel simplificado
  static Future<String?> _guardarArchivoSimple(
    Workbook workbook,
    AuditoriaModel auditoria
  ) async {
    try {
      debugPrint('Iniciando guardado de archivo...');

      // Obtener directorio de documentos
      final Directory appDir = await getApplicationDocumentsDirectory();
      debugPrint('Directorio de app obtenido: ${appDir.path}');

      final String excelDir = '${appDir.path}/${AppConstants.carpetaExcel}';
      debugPrint('Directorio de Excel: $excelDir');

      // Crear directorio si no existe
      await Directory(excelDir).create(recursive: true);
      debugPrint('Directorio creado');

      // Generar nombre de archivo único
      final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final String nombreArchivo = '${AppConstants.prefijoArchivo}${auditoria.numeroOrden}_$timestamp.xlsx';
      final String rutaCompleta = '$excelDir/$nombreArchivo';
      debugPrint('Ruta completa: $rutaCompleta');

      // Guardar archivo - aquí es donde probablemente está el error
      debugPrint('Intentando saveAsStream...');
      final List<int> bytes = workbook.saveAsStream();
      debugPrint('saveAsStream completado, bytes: ${bytes.length}');

      debugPrint('Creando archivo...');
      final File archivo = File(rutaCompleta);

      debugPrint('Escribiendo bytes...');
      await archivo.writeAsBytes(bytes);
      debugPrint('Archivo escrito exitosamente');

      return rutaCompleta;
    } catch (e) {
      debugPrint('Error al guardar archivo Excel: $e');
      debugPrint('Stack trace completo: $e');
      return null;
    }
  }

  // Guardar archivo Excel
  static Future<String?> _guardarArchivo(
    Workbook workbook,
    AuditoriaModel auditoria
  ) async {
    try {
      // Obtener directorio de documentos
      final Directory appDir = await getApplicationDocumentsDirectory();
      final String excelDir = '${appDir.path}/${AppConstants.carpetaExcel}';

      // Crear directorio si no existe
      await Directory(excelDir).create(recursive: true);

      // Generar nombre de archivo único
      final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final String nombreArchivo = '${AppConstants.prefijoArchivo}${auditoria.numeroOrden}_$timestamp.xlsx';
      final String rutaCompleta = '$excelDir/$nombreArchivo';

      // Guardar archivo
      final List<int> bytes = workbook.saveAsStream();
      final File archivo = File(rutaCompleta);
      await archivo.writeAsBytes(bytes);

      return rutaCompleta;
    } catch (e) {
      debugPrint('Error al guardar archivo Excel: $e');
      return null;
    }
  }

  // Abrir archivo Excel generado
  static Future<void> abrirArchivo(String rutaArchivo) async {
    try {
      await OpenFile.open(rutaArchivo);
    } catch (e) {
      debugPrint('Error al abrir archivo: $e');
    }
  }

  // Mostrar diálogo de éxito con opción de abrir archivo
  static void mostrarDialogoExito(
    BuildContext context,
    String rutaArchivo,
    VoidCallback onNuevaAuditoria
  ) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Excel Generado',
            style: AppStyles.subtitleStyle,
          ),
          content: const Text(
            'La auditoría se ha exportado exitosamente a Excel.',
            style: AppStyles.bodyStyle,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onNuevaAuditoria();
              },
              child: const Text('Nueva Auditoría'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await abrirArchivo(rutaArchivo);
              },
              style: AppStyles.primaryButtonStyle,
              child: const Text('Abrir Excel'),
            ),
          ],
        );
      },
    );
  }
}
