import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart';
import 'package:open_file/open_file.dart';
import '../models/auditoria_model.dart';
import '../utils/constants.dart';
import 'photo_service.dart';

class ExcelGenerator {
  // Generar archivo Excel con datos y fotos
  static Future<String?> generarExcel(AuditoriaModel auditoria) async {
    try {
      // Crear nuevo workbook
      final Workbook workbook = Workbook();
      final Worksheet worksheet = workbook.worksheets[0];
      
      // Configurar nombre de la hoja
      worksheet.name = 'Auditoría Movistar';
      
      // Configurar encabezados y datos generales
      await _configurarEncabezados(worksheet, auditoria);
      
      // Agregar datos de ítems y fotos
      await _agregarItemsYFotos(worksheet, auditoria);
      
      // Guardar archivo
      final String? rutaArchivo = await _guardarArchivo(workbook, auditoria);
      
      // Liberar recursos
      workbook.dispose();
      
      return rutaArchivo;
    } catch (e) {
      debugPrint('Error al generar Excel: $e');
      return null;
    }
  }

  // Configurar encabezados y datos generales
  static Future<void> _configurarEncabezados(
    Worksheet worksheet, 
    AuditoriaModel auditoria
  ) async {
    // Título principal
    worksheet.getRangeByName('A1:F1').merge();
    worksheet.getRangeByName('A1').setText('AUDITORÍA TÉCNICA MOVISTAR');
    worksheet.getRangeByName('A1').cellStyle.fontSize = 16;
    worksheet.getRangeByName('A1').cellStyle.bold = true;
    worksheet.getRangeByName('A1').cellStyle.hAlign = HAlignType.center;
    worksheet.getRangeByName('A1').cellStyle.backColor = '#0066CC';
    worksheet.getRangeByName('A1').cellStyle.fontColor = '#FFFFFF';

    // Datos generales
    int fila = 3;
    final Map<String, String> datosGenerales = {
      'Número de Orden:': auditoria.numeroOrden,
      'Nombre del Técnico:': auditoria.nombreTecnico,
      'Empresa Contratista:': auditoria.empresaContratista,
      'Tipo de Solicitud:': auditoria.tipoSolicitud,
      'Dirección:': auditoria.direccion,
      'Fecha de Auditoría:': auditoria.fechaAuditoria,
      'Observaciones:': auditoria.observaciones,
    };

    for (MapEntry<String, String> entry in datosGenerales.entries) {
      worksheet.getRangeByName('A$fila').setText(entry.key);
      worksheet.getRangeByName('A$fila').cellStyle.bold = true;
      worksheet.getRangeByName('B$fila').setText(entry.value);
      fila++;
    }

    // Encabezados de la tabla de ítems
    fila += 2;
    final List<String> encabezados = [
      'Ítem',
      'Fotos Normales (1-3)',
      'Tiene Hallazgo',
      'Fotos Hallazgo (1-3)',
      'Estado'
    ];

    for (int i = 0; i < encabezados.length; i++) {
      final String celda = String.fromCharCode(65 + i) + fila.toString();
      worksheet.getRangeByName(celda).setText(encabezados[i]);
      worksheet.getRangeByName(celda).cellStyle.bold = true;
      worksheet.getRangeByName(celda).cellStyle.backColor = '#4A90E2';
      worksheet.getRangeByName(celda).cellStyle.fontColor = '#FFFFFF';
    }
  }

  // Agregar ítems y fotos al Excel
  static Future<void> _agregarItemsYFotos(
    Worksheet worksheet,
    AuditoriaModel auditoria
  ) async {
    int filaInicial = 12; // Después de los encabezados

    for (int i = 0; i < AuditoriaModel.itemsAuditoria.length; i++) {
      final String item = AuditoriaModel.itemsAuditoria[i];
      final int filaActual = filaInicial + i;

      // Configurar altura de fila para las fotos
      worksheet.setRowHeightInPixels(filaActual, 120);

      // Nombre del ítem
      worksheet.getRangeByName('A$filaActual').setText(item);

      // Fotos normales
      final List<File> fotosNormales = auditoria.fotosNormales[item] ?? [];
      if (fotosNormales.isNotEmpty) {
        // Insertar primera foto en la celda principal
        await _insertarFoto(worksheet, fotosNormales[0], 'B$filaActual');

        // Si hay más fotos, agregarlas en celdas adyacentes
        for (int j = 1; j < fotosNormales.length && j < 3; j++) {
          final String celdaExtra = String.fromCharCode(66 + j) + filaActual.toString(); // C, D, etc.
          await _insertarFoto(worksheet, fotosNormales[j], celdaExtra);
        }
      } else {
        worksheet.getRangeByName('B$filaActual').setText('Sin fotos');
      }

      // Tiene hallazgo
      final bool tieneHallazgo = auditoria.tieneHallazgo[item] ?? false;
      worksheet.getRangeByName('C$filaActual').setText(tieneHallazgo ? 'SÍ' : 'NO');

      // Fotos de hallazgo
      if (tieneHallazgo) {
        final List<File> fotosHallazgos = auditoria.fotosHallazgos[item] ?? [];
        if (fotosHallazgos.isNotEmpty) {
          // Insertar primera foto de hallazgo
          await _insertarFoto(worksheet, fotosHallazgos[0], 'D$filaActual');

          // Si hay más fotos de hallazgo, agregarlas en celdas adyacentes
          for (int j = 1; j < fotosHallazgos.length && j < 3; j++) {
            final String celdaExtra = String.fromCharCode(68 + j) + filaActual.toString(); // E, F, etc.
            await _insertarFoto(worksheet, fotosHallazgos[j], celdaExtra);
          }
        } else {
          worksheet.getRangeByName('D$filaActual').setText('Pendiente');
        }
      } else {
        worksheet.getRangeByName('D$filaActual').setText('N/A');
      }

      // Estado
      String estado = 'Completo';
      if (fotosNormales.isEmpty) {
        estado = 'Faltan fotos normales';
      } else if (tieneHallazgo && (auditoria.fotosHallazgos[item]?.isEmpty ?? true)) {
        estado = 'Faltan fotos de hallazgo';
      }
      worksheet.getRangeByName('E$filaActual').setText(estado);
    }

    // Ajustar ancho de columnas
    worksheet.autoFitColumn(1); // Columna A
    worksheet.setColumnWidthInPixels(2, 150); // Columna B (foto normal)
    worksheet.autoFitColumn(3); // Columna C
    worksheet.setColumnWidthInPixels(4, 150); // Columna D (foto hallazgo)
    worksheet.autoFitColumn(5); // Columna E
  }

  // Insertar foto en una celda específica
  static Future<void> _insertarFoto(
    Worksheet worksheet,
    File archivoFoto,
    String celda
  ) async {
    try {
      final Uint8List? bytesImagen = await PhotoService.obtenerBytesImagen(archivoFoto);
      if (bytesImagen != null) {
        // Obtener coordenadas de la celda
        final Range range = worksheet.getRangeByName(celda);
        final int fila = range.row;
        final int columna = range.column;

        // Agregar imagen al worksheet usando el método correcto
        final Picture picture = worksheet.pictures.addBase64(fila, columna,
            base64.encode(bytesImagen));

        // Configurar tamaño de la imagen
        picture.width = 140;
        picture.height = 100;
      }
    } catch (e) {
      debugPrint('Error al insertar foto en $celda: $e');
      worksheet.getRangeByName(celda).setText('Error al cargar foto');
    }
  }

  // Guardar archivo Excel
  static Future<String?> _guardarArchivo(
    Workbook workbook,
    AuditoriaModel auditoria
  ) async {
    try {
      // Obtener directorio de documentos
      final Directory appDir = await getApplicationDocumentsDirectory();
      final String excelDir = '${appDir.path}/${AppConstants.carpetaExcel}';

      // Crear directorio si no existe
      await Directory(excelDir).create(recursive: true);

      // Generar nombre de archivo único
      final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final String nombreArchivo = '${AppConstants.prefijoArchivo}${auditoria.numeroOrden}_$timestamp.xlsx';
      final String rutaCompleta = '$excelDir/$nombreArchivo';

      // Guardar archivo
      final List<int> bytes = workbook.saveAsStream();
      final File archivo = File(rutaCompleta);
      await archivo.writeAsBytes(bytes);

      return rutaCompleta;
    } catch (e) {
      debugPrint('Error al guardar archivo Excel: $e');
      return null;
    }
  }

  // Abrir archivo Excel generado
  static Future<void> abrirArchivo(String rutaArchivo) async {
    try {
      await OpenFile.open(rutaArchivo);
    } catch (e) {
      debugPrint('Error al abrir archivo: $e');
    }
  }

  // Mostrar diálogo de éxito con opción de abrir archivo
  static void mostrarDialogoExito(
    BuildContext context,
    String rutaArchivo,
    VoidCallback onNuevaAuditoria
  ) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Excel Generado',
            style: AppStyles.subtitleStyle,
          ),
          content: const Text(
            'La auditoría se ha exportado exitosamente a Excel.',
            style: AppStyles.bodyStyle,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onNuevaAuditoria();
              },
              child: const Text('Nueva Auditoría'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await abrirArchivo(rutaArchivo);
              },
              style: AppStyles.primaryButtonStyle,
              child: const Text('Abrir Excel'),
            ),
          ],
        );
      },
    );
  }
}
