import 'dart:io';

class AuditoriaModel {
  // Datos generales del formulario
  String numeroOrden;
  String nombreTecnico;
  String empresaContratista;
  String tipoSolicitud;
  String direccion;
  String fechaAuditoria;
  String observaciones;

  // Fotos por ítem - cada ítem puede tener foto normal y foto de hallazgo
  Map<String, File?> fotosNormales;
  Map<String, File?> fotosHallazgos;
  Map<String, bool> tieneHallazgo;

  // Lista de ítems que se van a auditar
  static const List<String> itemsAuditoria = [
    'Roseta',
    'CTO',
    'Estética Interna',
    'Estética Externa',
    'HGU/ONT',
    'Cableado',
    'Señalización',
    'Test de velocidad'
  ];

  AuditoriaModel({
    this.numeroOrden = '',
    this.nombreTecnico = '',
    this.empresaContratista = '',
    this.tipoSolicitud = '',
    this.direccion = '',
    this.fechaAuditoria = '',
    this.observaciones = '',
  }) : fotosNormales = {},
       fotosHallazgos = {},
       tieneHallazgo = {} {
    // Inicializar mapas para cada ítem
    for (String item in itemsAuditoria) {
      fotosNormales[item] = null;
      fotosHallazgos[item] = null;
      tieneHallazgo[item] = false;
    }
  }

  // Método para limpiar todos los datos (nueva auditoría)
  void limpiarDatos() {
    numeroOrden = '';
    nombreTecnico = '';
    empresaContratista = '';
    tipoSolicitud = '';
    direccion = '';
    fechaAuditoria = '';
    observaciones = '';
    
    for (String item in itemsAuditoria) {
      fotosNormales[item] = null;
      fotosHallazgos[item] = null;
      tieneHallazgo[item] = false;
    }
  }

  // Verificar si la auditoría está completa
  bool get estaCompleta {
    // Verificar que los datos básicos estén llenos
    if (numeroOrden.isEmpty || nombreTecnico.isEmpty || empresaContratista.isEmpty) {
      return false;
    }
    
    // Verificar que todos los ítems tengan al menos una foto normal
    for (String item in itemsAuditoria) {
      if (fotosNormales[item] == null) {
        return false;
      }
      // Si tiene hallazgo, debe tener foto de hallazgo
      if (tieneHallazgo[item] == true && fotosHallazgos[item] == null) {
        return false;
      }
    }
    
    return true;
  }

  // Contar total de fotos tomadas
  int get totalFotos {
    int count = 0;
    for (String item in itemsAuditoria) {
      if (fotosNormales[item] != null) count++;
      if (fotosHallazgos[item] != null) count++;
    }
    return count;
  }
}
