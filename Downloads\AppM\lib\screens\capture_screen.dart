import 'dart:io';
import 'package:flutter/material.dart';
import '../models/auditoria_model.dart';
import '../utils/constants.dart';
import '../services/photo_service.dart';

class CaptureScreen extends StatefulWidget {
  final AuditoriaModel auditoria;

  const CaptureScreen({super.key, required this.auditoria});

  @override
  State<CaptureScreen> createState() => _CaptureScreenState();
}

class _CaptureScreenState extends State<CaptureScreen> {
  int _itemActual = 0;
  bool _cargandoFoto = false;

  @override
  Widget build(BuildContext context) {
    final String item = AuditoriaModel.itemsAuditoria[_itemActual];
    final bool tieneHallazgo = widget.auditoria.tieneHallazgo[item] ?? false;
    final File? fotoNormal = widget.auditoria.fotosNormales[item];
    final File? fotoHallazgo = widget.auditoria.fotosHallazgos[item];

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Ítem ${_itemActual + 1} de ${AuditoriaModel.itemsAuditoria.length}',
          style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppConstants.movistarBlue,
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppConstants.movistarBlue.withOpacity(0.1),
              Colors.white,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Progreso
              Container(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    LinearProgressIndicator(
                      value: (_itemActual + 1) / AuditoriaModel.itemsAuditoria.length,
                      backgroundColor: Colors.grey[300],
                      valueColor: AlwaysStoppedAnimation<Color>(AppConstants.movistarBlue),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Progreso: ${_itemActual + 1}/${AuditoriaModel.itemsAuditoria.length}',
                      style: AppStyles.bodyStyle.copyWith(fontSize: 14),
                    ),
                  ],
                ),
              ),

              // Contenido principal
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Título del ítem actual
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: AppConstants.movistarBlue,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: AppConstants.movistarBlue.withOpacity(0.3),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Icon(
                                Icons.camera_alt_outlined,
                                color: Colors.white,
                                size: 32,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    item,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 24,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'Tome las fotos necesarias',
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.9),
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 32),

                      // Sección foto normal
                      _buildSeccionFoto(
                        titulo: 'Foto Normal',
                        descripcion: 'Tome una foto del estado actual de $item',
                        foto: fotoNormal,
                        onTomarFoto: () => _tomarFoto(false),
                        icono: Icons.camera_outlined,
                        color: AppConstants.movistarBlue,
                      ),

                      const SizedBox(height: 24),

                      // Switch para hallazgo
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.orange[50],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.orange.withOpacity(0.3)),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.warning_outlined, color: Colors.orange[700]),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                '¿Se encontró algún hallazgo o defecto?',
                                style: AppStyles.bodyStyle.copyWith(
                                  fontWeight: FontWeight.w500,
                                  color: Colors.orange[700],
                                ),
                              ),
                            ),
                            Switch(
                              value: tieneHallazgo,
                              onChanged: (value) {
                                setState(() {
                                  widget.auditoria.tieneHallazgo[item] = value;
                                  if (!value) {
                                    widget.auditoria.fotosHallazgos[item] = null;
                                  }
                                });
                              },
                              activeColor: AppConstants.movistarRed,
                            ),
                          ],
                        ),
                      ),

                      // Sección foto de hallazgo (solo si tiene hallazgo)
                      if (tieneHallazgo) ...[
                        const SizedBox(height: 24),
                        _buildSeccionFoto(
                          titulo: 'Foto de Hallazgo',
                          descripcion: 'Tome una foto que muestre claramente el problema encontrado',
                          foto: fotoHallazgo,
                          onTomarFoto: () => _tomarFoto(true),
                          icono: Icons.report_problem_outlined,
                          color: AppConstants.movistarRed,
                        ),
                      ],

                      const SizedBox(height: 40),

                      // Estado del ítem actual
                      _buildEstadoItem(item, fotoNormal, tieneHallazgo, fotoHallazgo),
                    ],
                  ),
                ),
              ),

              // Botones de navegación
              Container(
                padding: const EdgeInsets.all(24.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, -5),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // Botón Anterior
                    if (_itemActual > 0)
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: _itemAnterior,
                          icon: const Icon(Icons.arrow_back),
                          label: const Text('Anterior'),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            side: BorderSide(color: AppConstants.movistarBlue),
                            foregroundColor: AppConstants.movistarBlue,
                          ),
                        ),
                      ),

                    if (_itemActual > 0) const SizedBox(width: 16),

                    // Botón Siguiente/Finalizar
                    Expanded(
                      flex: 2,
                      child: ElevatedButton.icon(
                        onPressed: _puedeAvanzar() ? _siguienteItem : null,
                        icon: Icon(_itemActual == AuditoriaModel.itemsAuditoria.length - 1
                            ? Icons.check_circle_outline
                            : Icons.arrow_forward),
                        label: Text(_itemActual == AuditoriaModel.itemsAuditoria.length - 1
                            ? 'Finalizar'
                            : 'Siguiente'),
                        style: _puedeAvanzar()
                            ? AppStyles.primaryButtonStyle
                            : ElevatedButton.styleFrom(
                                backgroundColor: Colors.grey[400],
                                foregroundColor: Colors.grey[600],
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Widget para sección de foto
  Widget _buildSeccionFoto({
    required String titulo,
    required String descripcion,
    required File? foto,
    required VoidCallback onTomarFoto,
    required IconData icono,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icono, color: color, size: 24),
              const SizedBox(width: 12),
              Text(
                titulo,
                style: AppStyles.subtitleStyle.copyWith(
                  color: color,
                  fontSize: 18,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            descripcion,
            style: AppStyles.bodyStyle.copyWith(fontSize: 14),
          ),
          const SizedBox(height: 16),

          if (foto != null) ...[
            // Mostrar foto tomada
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.file(
                  foto,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => PhotoService.mostrarPreviewFoto(context, foto),
                    icon: const Icon(Icons.visibility_outlined),
                    label: const Text('Ver Foto'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: color,
                      side: BorderSide(color: color),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _cargandoFoto ? null : onTomarFoto,
                    icon: _cargandoFoto
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.camera_alt_outlined),
                    label: const Text('Retomar'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: color,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ] else ...[
            // Botón para tomar foto
            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton.icon(
                onPressed: _cargandoFoto ? null : onTomarFoto,
                icon: _cargandoFoto
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Icon(icono),
                label: Text(_cargandoFoto ? 'Tomando foto...' : 'Tomar Foto'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: color,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Widget para mostrar estado del ítem
  Widget _buildEstadoItem(String item, File? fotoNormal, bool tieneHallazgo, File? fotoHallazgo) {
    bool completo = fotoNormal != null && (!tieneHallazgo || fotoHallazgo != null);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: completo ? Colors.green[50] : Colors.orange[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: completo
              ? AppConstants.movistarGreen.withOpacity(0.3)
              : Colors.orange.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            completo ? Icons.check_circle_outline : Icons.pending_outlined,
            color: completo ? AppConstants.movistarGreen : Colors.orange[700],
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              completo ? 'Ítem completado' : 'Faltan fotos por tomar',
              style: AppStyles.bodyStyle.copyWith(
                color: completo ? AppConstants.movistarGreen : Colors.orange[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Tomar foto
  Future<void> _tomarFoto(bool esHallazgo) async {
    setState(() {
      _cargandoFoto = true;
    });

    try {
      final String item = AuditoriaModel.itemsAuditoria[_itemActual];
      final File? foto = await PhotoService.mostrarDialogoTomarFoto(
        context,
        item,
        esHallazgo: esHallazgo,
      );

      if (foto != null) {
        setState(() {
          if (esHallazgo) {
            widget.auditoria.fotosHallazgos[item] = foto;
          } else {
            widget.auditoria.fotosNormales[item] = foto;
          }
        });
      }
    } catch (e) {
      _mostrarMensajeError('Error al tomar la foto: $e');
    } finally {
      setState(() {
        _cargandoFoto = false;
      });
    }
  }

  // Verificar si puede avanzar al siguiente ítem
  bool _puedeAvanzar() {
    final String item = AuditoriaModel.itemsAuditoria[_itemActual];
    final bool tieneHallazgo = widget.auditoria.tieneHallazgo[item] ?? false;
    final File? fotoNormal = widget.auditoria.fotosNormales[item];
    final File? fotoHallazgo = widget.auditoria.fotosHallazgos[item];

    // Debe tener foto normal
    if (fotoNormal == null) return false;

    // Si tiene hallazgo, debe tener foto de hallazgo
    if (tieneHallazgo && fotoHallazgo == null) return false;

    return true;
  }

  // Ir al ítem anterior
  void _itemAnterior() {
    if (_itemActual > 0) {
      setState(() {
        _itemActual--;
      });
    }
  }

  // Ir al siguiente ítem o finalizar
  void _siguienteItem() {
    if (!_puedeAvanzar()) return;

    if (_itemActual < AuditoriaModel.itemsAuditoria.length - 1) {
      // Siguiente ítem
      setState(() {
        _itemActual++;
      });
    } else {
      // Finalizar captura
      _finalizarCaptura();
    }
  }

  // Finalizar captura y regresar
  void _finalizarCaptura() {
    // Mostrar mensaje de éxito
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Todas las fotos han sido capturadas exitosamente'),
        backgroundColor: AppConstants.movistarGreen,
        behavior: SnackBarBehavior.floating,
      ),
    );

    // Regresar a la pantalla principal
    Navigator.of(context).popUntil((route) => route.isFirst);
  }

  // Mostrar mensaje de error
  void _mostrarMensajeError(String mensaje) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(mensaje),
        backgroundColor: AppConstants.movistarRed,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
