import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'screens/home_screen.dart';
import 'utils/constants.dart';

void main() {
  runApp(const MovistarAuditorApp());
}

class MovistarAuditorApp extends StatelessWidget {
  const MovistarAuditorApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Configurar orientación de pantalla
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Configurar barra de estado
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: AppConstants.movistarBlue,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    return MaterialApp(
      title: AppConstants.appTitle,
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        // Colores principales
        primarySwatch: _createMaterialColor(AppConstants.movistarBlue),
        primaryColor: AppConstants.movistarBlue,
        colorScheme: ColorScheme.fromSeed(
          seedColor: AppConstants.movistarBlue,
          brightness: Brightness.light,
        ),
        
        // Configuración de AppBar
        appBarTheme: const AppBarTheme(
          backgroundColor: AppConstants.movistarBlue,
          foregroundColor: Colors.white,
          elevation: 0,
          centerTitle: true,
          titleTextStyle: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
          iconTheme: IconThemeData(color: Colors.white),
        ),
        
        // Configuración de botones
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: AppStyles.primaryButtonStyle,
        ),
        
        // Configuración de campos de texto
        inputDecorationTheme: InputDecorationTheme(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: AppConstants.movistarBlue),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: AppConstants.movistarBlue, width: 2),
          ),
          labelStyle: const TextStyle(color: AppConstants.movistarGray),
        ),
        
        // Configuración de SnackBar
        snackBarTheme: const SnackBarThemeData(
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(8)),
          ),
        ),
        
        // Configuración de Card
        cardTheme: CardTheme(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        
        // Configuración de texto
        textTheme: const TextTheme(
          headlineLarge: AppStyles.titleStyle,
          headlineMedium: AppStyles.subtitleStyle,
          bodyLarge: AppStyles.bodyStyle,
          bodyMedium: AppStyles.bodyStyle,
        ),
        
        // Configuración de iconos
        iconTheme: const IconThemeData(
          color: AppConstants.movistarBlue,
        ),
        
        // Usar Material 3
        useMaterial3: true,
      ),
      home: const HomeScreen(),
    );
  }

  // Crear MaterialColor desde Color
  MaterialColor _createMaterialColor(Color color) {
    List strengths = <double>[.05];
    Map<int, Color> swatch = {};
    final int r = color.red, g = color.green, b = color.blue;

    for (int i = 1; i < 10; i++) {
      strengths.add(0.1 * i);
    }
    
    for (double strength in strengths) {
      final double ds = 0.5 - strength;
      swatch[(strength * 1000).round()] = Color.fromRGBO(
        r + ((ds < 0 ? r : (255 - r)) * ds).round(),
        g + ((ds < 0 ? g : (255 - g)) * ds).round(),
        b + ((ds < 0 ? b : (255 - b)) * ds).round(),
        1,
      );
    }
    
    return MaterialColor(color.value, swatch);
  }
}
