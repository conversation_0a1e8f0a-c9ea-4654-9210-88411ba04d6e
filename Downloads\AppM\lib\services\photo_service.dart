import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/constants.dart';

class PhotoService {
  static final ImagePicker _picker = ImagePicker();

  // Solicitar permisos de cámara y almacenamiento
  static Future<bool> solicitarPermisos() async {
    Map<Permission, PermissionStatus> permisos = await [
      Permission.camera,
      Permission.storage,
      Permission.manageExternalStorage,
    ].request();

    return permisos[Permission.camera] == PermissionStatus.granted;
  }

  // Tomar foto desde la cámara
  static Future<File?> tomarFoto() async {
    try {
      // Verificar permisos
      bool permisosOk = await solicitarPermisos();
      if (!permisosOk) {
        throw Exception('Permisos de cámara no concedidos');
      }

      // Tomar la foto
      final XFile? foto = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: AppConstants.maxImageWidth,
        maxHeight: AppConstants.maxImageHeight,
        imageQuality: AppConstants.imageQuality,
      );

      if (foto != null) {
        // Guardar la foto en el directorio de la app
        return await _guardarFoto(foto);
      }
      return null;
    } catch (e) {
      debugPrint('Error al tomar foto: $e');
      return null;
    }
  }

  // Guardar foto en el directorio de la aplicación
  static Future<File> _guardarFoto(XFile foto) async {
    final Directory appDir = await getApplicationDocumentsDirectory();
    final String fotosDir = '${appDir.path}/${AppConstants.carpetaFotos}';
    
    // Crear directorio si no existe
    await Directory(fotosDir).create(recursive: true);
    
    // Generar nombre único para la foto
    final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final String nombreArchivo = 'foto_$timestamp.jpg';
    final String rutaCompleta = '$fotosDir/$nombreArchivo';
    
    // Copiar archivo
    final File archivoGuardado = await File(foto.path).copy(rutaCompleta);
    return archivoGuardado;
  }

  // Obtener bytes de una imagen para embeber en Excel
  static Future<Uint8List?> obtenerBytesImagen(File? archivo) async {
    if (archivo == null || !await archivo.exists()) {
      return null;
    }
    
    try {
      return await archivo.readAsBytes();
    } catch (e) {
      debugPrint('Error al leer bytes de imagen: $e');
      return null;
    }
  }

  // Limpiar fotos temporales (opcional, para liberar espacio)
  static Future<void> limpiarFotosTemporales() async {
    try {
      final Directory appDir = await getApplicationDocumentsDirectory();
      final String fotosDir = '${appDir.path}/${AppConstants.carpetaFotos}';
      final Directory directorio = Directory(fotosDir);
      
      if (await directorio.exists()) {
        await directorio.delete(recursive: true);
      }
    } catch (e) {
      debugPrint('Error al limpiar fotos temporales: $e');
    }
  }

  // Mostrar diálogo de confirmación para tomar foto
  static Future<File?> mostrarDialogoTomarFoto(
    BuildContext context, 
    String item, 
    {bool esHallazgo = false}
  ) async {
    String titulo = esHallazgo 
        ? 'Foto de Hallazgo - $item'
        : 'Foto Normal - $item';
    
    String mensaje = esHallazgo
        ? 'Tome una foto que muestre claramente el problema o defecto encontrado en $item'
        : 'Tome una foto del estado actual de $item';

    bool? confirmar = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(titulo, style: AppStyles.subtitleStyle),
          content: Text(mensaje, style: AppStyles.bodyStyle),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancelar'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: AppStyles.primaryButtonStyle,
              child: const Text('Tomar Foto'),
            ),
          ],
        );
      },
    );

    if (confirmar == true) {
      return await tomarFoto();
    }
    return null;
  }

  // Mostrar preview de la foto tomada
  static void mostrarPreviewFoto(BuildContext context, File foto) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                child: const Text(
                  'Foto Capturada',
                  style: AppStyles.subtitleStyle,
                ),
              ),
              Container(
                constraints: const BoxConstraints(maxHeight: 400),
                child: Image.file(foto, fit: BoxFit.contain),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: AppStyles.primaryButtonStyle,
                  child: const Text('Cerrar'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
