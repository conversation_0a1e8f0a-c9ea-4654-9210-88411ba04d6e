import 'package:flutter/material.dart';
import '../utils/constants.dart';
import '../models/auditoria_model.dart';
import '../services/excel_generator.dart';
import 'form_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  // Instancia global de la auditoría actual
  static AuditoriaModel _auditoriaActual = AuditoriaModel();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          AppConstants.appTitle,
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppConstants.movistarBlue,
        elevation: 0,
        centerTitle: true,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppConstants.movistarBlue.withOpacity(0.1),
              Colors.white,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo o icono principal
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: AppConstants.movistarBlue,
                    borderRadius: BorderRadius.circular(60),
                    boxShadow: [
                      BoxShadow(
                        color: AppConstants.movistarBlue.withOpacity(0.3),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.assignment_outlined,
                    size: 60,
                    color: Colors.white,
                  ),
                ),
                
                const SizedBox(height: 40),
                
                // Título de bienvenida
                const Text(
                  'Auditoría Técnica',
                  style: AppStyles.titleStyle,
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 8),
                
                const Text(
                  'Sistema de inspección en campo',
                  style: AppStyles.subtitleStyle,
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 60),
                
                // Información de la auditoría actual
                _buildInfoAuditoriaActual(),
                
                const SizedBox(height: 40),
                
                // Botones principales
                Column(
                  children: [
                    // Botón Nueva Auditoría
                    SizedBox(
                      width: double.infinity,
                      height: 56,
                      child: ElevatedButton.icon(
                        onPressed: _iniciarNuevaAuditoria,
                        icon: const Icon(Icons.add_circle_outline, size: 24),
                        label: const Text(
                          AppConstants.nuevaAuditoria,
                          style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                        ),
                        style: AppStyles.primaryButtonStyle,
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Botón Exportar Excel
                    SizedBox(
                      width: double.infinity,
                      height: 56,
                      child: ElevatedButton.icon(
                        onPressed: _auditoriaActual.estaCompleta ? _exportarExcel : null,
                        icon: const Icon(Icons.file_download_outlined, size: 24),
                        label: const Text(
                          AppConstants.exportarExcel,
                          style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                        ),
                        style: _auditoriaActual.estaCompleta 
                            ? AppStyles.secondaryButtonStyle
                            : ElevatedButton.styleFrom(
                                backgroundColor: Colors.grey[400],
                                foregroundColor: Colors.grey[600],
                              ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 40),
                
                // Información adicional
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppConstants.movistarLightBlue.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: AppConstants.movistarBlue,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Complete todos los datos y fotos para habilitar la exportación',
                          style: AppStyles.bodyStyle.copyWith(
                            color: AppConstants.movistarBlue,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Widget para mostrar información de la auditoría actual
  Widget _buildInfoAuditoriaActual() {
    if (_auditoriaActual.numeroOrden.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Row(
          children: [
            Icon(Icons.assignment_late_outlined, color: Colors.grey[600]),
            const SizedBox(width: 12),
            Text(
              'No hay auditoría en progreso',
              style: AppStyles.bodyStyle.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppConstants.movistarGreen.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.assignment_outlined, color: AppConstants.movistarGreen),
              const SizedBox(width: 12),
              Text(
                'Auditoría en Progreso',
                style: AppStyles.subtitleStyle.copyWith(
                  color: AppConstants.movistarGreen,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Orden: ${_auditoriaActual.numeroOrden}',
            style: AppStyles.bodyStyle.copyWith(fontWeight: FontWeight.w500),
          ),
          if (_auditoriaActual.nombreTecnico.isNotEmpty)
            Text(
              'Técnico: ${_auditoriaActual.nombreTecnico}',
              style: AppStyles.bodyStyle,
            ),
          if (_auditoriaActual.nombreAuditor.isNotEmpty)
            Text(
              'Auditor: ${_auditoriaActual.nombreAuditor}',
              style: AppStyles.bodyStyle,
            ),
          if (_auditoriaActual.ciudad.isNotEmpty)
            Text(
              'Ciudad: ${_auditoriaActual.ciudad}',
              style: AppStyles.bodyStyle,
            ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Fotos: ${_auditoriaActual.totalFotos}',
                style: AppStyles.bodyStyle.copyWith(fontSize: 14),
              ),
              Text(
                _auditoriaActual.estaCompleta ? 'Completa ✓' : 'En progreso...',
                style: AppStyles.bodyStyle.copyWith(
                  fontSize: 14,
                  color: _auditoriaActual.estaCompleta 
                      ? AppConstants.movistarGreen 
                      : AppConstants.movistarBlue,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Iniciar nueva auditoría
  void _iniciarNuevaAuditoria() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FormScreen(auditoria: _auditoriaActual),
      ),
    ).then((_) {
      // Actualizar la pantalla cuando regrese del formulario
      setState(() {});
    });
  }

  // Exportar auditoría a Excel
  void _exportarExcel() async {
    if (!_auditoriaActual.estaCompleta) {
      _mostrarMensajeError('Complete todos los campos y fotos antes de exportar');
      return;
    }

    // Mostrar indicador de carga
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      // Generar Excel
      final String? rutaArchivo = await ExcelGenerator.generarExcel(_auditoriaActual);
      
      // Cerrar indicador de carga
      Navigator.of(context).pop();

      if (rutaArchivo != null) {
        // Mostrar diálogo de éxito
        ExcelGenerator.mostrarDialogoExito(
          context,
          rutaArchivo,
          _limpiarAuditoria,
        );
      } else {
        _mostrarMensajeError('Error al generar el archivo Excel');
      }
    } catch (e) {
      // Cerrar indicador de carga
      Navigator.of(context).pop();
      _mostrarMensajeError('Error inesperado: $e');
    }
  }

  // Limpiar auditoría actual
  void _limpiarAuditoria() {
    setState(() {
      _auditoriaActual.limpiarDatos();
    });
  }

  // Mostrar mensaje de error
  void _mostrarMensajeError(String mensaje) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(mensaje),
        backgroundColor: AppConstants.movistarRed,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  // Getter estático para acceder a la auditoría desde otras pantallas
  static AuditoriaModel get auditoriaActual => _auditoriaActual;
}
