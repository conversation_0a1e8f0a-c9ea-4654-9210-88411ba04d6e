# Auditoría Movistar App

Aplicación móvil desarrollada en Flutter para realizar auditorías técnicas en campo para Movistar. Permite capturar datos, tomar fotos y generar reportes en Excel.

## 🚀 Características

- **Formulario de datos generales**: Captura información básica de la auditoría
- **Captura de fotos por ítem**: Sistema organizado para tomar fotos de diferentes elementos
- **Detección de hallazgos**: Permite marcar y fotografiar problemas encontrados
- **Generación de Excel**: Crea reportes con datos y fotos embebidas
- **Interfaz intuitiva**: Diseño moderno con colores corporativos de Movistar

## 📱 Pantallas

1. **Home Screen**: Pantalla principal con opciones de nueva auditoría y exportar
2. **Form Screen**: Formulario para capturar datos generales
3. **Capture Screen**: Interfaz para tomar fotos por ítem con detección de hallazgos

## 🛠️ Tecnologías

- **Flutter**: Framework principal
- **Dart**: Lenguaje de programación
- **image_picker**: Captura de fotos desde cámara
- **path_provider**: Gestión de archivos locales
- **permission_handler**: Manejo de permisos
- **syncfusion_flutter_xlsio**: Generación de archivos Excel
- **open_file**: Apertura de archivos generados

## 📋 Ítems de Auditoría

La aplicación audita los siguientes elementos:
- Roseta
- CTO
- Estética Interna
- Estética Externa
- HGU/ONT
- Cableado
- Señalización
- Documentación

## 🔧 Instalación y Configuración

### Prerrequisitos
- Flutter SDK (versión 3.10.0 o superior)
- Android Studio o VS Code
- Dispositivo Android o emulador

### Pasos de instalación

1. **Clonar o descargar el proyecto**
   ```bash
   cd Downloads/AppM
   ```

2. **Instalar dependencias**
   ```bash
   flutter pub get
   ```

3. **Verificar configuración de Flutter**
   ```bash
   flutter doctor
   ```

4. **Ejecutar la aplicación**
   ```bash
   flutter run
   ```

## 📱 Permisos Requeridos

La aplicación requiere los siguientes permisos:
- **Cámara**: Para tomar fotos durante la auditoría
- **Almacenamiento**: Para guardar fotos y archivos Excel
- **Gestión de archivos externos**: Para acceso completo al almacenamiento

## 🎯 Flujo de Uso

1. **Iniciar Nueva Auditoría**
   - Tocar "Nueva Auditoría" en la pantalla principal
   - Llenar el formulario con datos generales
   - Continuar a captura de fotos

2. **Capturar Fotos**
   - Para cada ítem, tomar foto del estado actual
   - Si hay hallazgo, marcar el switch y tomar foto adicional
   - Navegar entre ítems usando botones Anterior/Siguiente

3. **Exportar Auditoría**
   - Regresar a pantalla principal
   - Tocar "Exportar Excel" (habilitado cuando esté completa)
   - El archivo se genera y se puede abrir directamente

## 📁 Estructura del Proyecto

```
lib/
├── main.dart                    # Punto de entrada
├── models/
│   └── auditoria_model.dart     # Modelo de datos de auditoría
├── screens/
│   ├── home_screen.dart         # Pantalla principal
│   ├── form_screen.dart         # Formulario de datos
│   └── capture_screen.dart      # Captura de fotos
├── services/
│   ├── photo_service.dart       # Gestión de fotos
│   └── excel_generator.dart     # Generación de Excel
└── utils/
    └── constants.dart           # Constantes y estilos
```

## 🎨 Diseño

- **Colores**: Paleta corporativa de Movistar (azul #0066CC, verde #00A651)
- **Tipografía**: Material Design con jerarquía clara
- **Iconografía**: Material Icons para consistencia
- **Responsive**: Adaptado para diferentes tamaños de pantalla

## 📊 Archivo Excel Generado

El archivo Excel incluye:
- **Datos generales**: Información del formulario
- **Tabla de ítems**: Con fotos embebidas en celdas
- **Estado de cada ítem**: Completo/Pendiente
- **Formato profesional**: Con colores y estilos corporativos

## 🔍 Solución de Problemas

### Error de permisos
- Verificar que los permisos de cámara estén habilitados
- En Android: Configuración > Aplicaciones > Auditoría Movistar > Permisos

### Error al generar Excel
- Verificar espacio disponible en el dispositivo
- Asegurar que todas las fotos se hayan tomado correctamente

### Problemas de rendimiento
- Limpiar caché de la aplicación
- Reiniciar la aplicación si hay muchas fotos en memoria

## 📞 Soporte

Para soporte técnico o reportar problemas, contactar al equipo de desarrollo.

## 📄 Licencia

Aplicación desarrollada para uso interno de Movistar.
