import 'package:flutter/material.dart';

class AppConstants {
  // Colores de Movistar
  static const Color movistarBlue = Color(0xFF0066CC);
  static const Color movistarLightBlue = Color(0xFF4A90E2);
  static const Color movistarGreen = Color(0xFF00A651);
  static const Color movistarRed = Color(0xFFE53E3E);
  static const Color movistarGray = Color(0xFF666666);

  // Textos de la aplicación
  static const String appTitle = 'Auditoría Movistar';
  static const String nuevaAuditoria = 'Nueva Auditoría';
  static const String exportarExcel = 'Exportar Excel';
  static const String datosGenerales = 'Datos Generales';
  static const String capturarFotos = 'Capturar Fotos';
  static const String generarReporte = 'Generar Reporte';

  // Tipos de solicitud
  static const List<String> tiposSolicitud = [
    'Alta',
    'Avería',
    'Traslado'
  ];

  // Empresas contratistas comunes
  static const List<String> empresasContratistas = [
    'Cobra',
    'Axtel',
    'Alestra',
    'Megacable',
    'Totalplay',
    'Otra'
  ];

  // Mensajes de validación
  static const String campoRequerido = 'Este campo es requerido';
  static const String fotoRequerida = 'Debe tomar al menos una foto';
  static const String auditoriaIncompleta = 'Complete todos los campos y fotos antes de exportar';
  static const String auditoriaExportada = 'Auditoría exportada exitosamente';
  static const String errorExportacion = 'Error al exportar la auditoría';

  // Configuración de fotos
  static const double maxImageWidth = 800;
  static const double maxImageHeight = 600;
  static const int imageQuality = 85;

  // Rutas de archivos
  static const String carpetaFotos = 'auditorias_fotos';
  static const String carpetaExcel = 'auditorias_excel';
  static const String prefijoArchivo = 'auditoria_movistar_';
}

class AppStyles {
  // Estilos de texto modernos
  static const TextStyle titleStyle = TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.bold,
    color: AppConstants.movistarBlue,
    letterSpacing: 0.5,
  );

  static const TextStyle subtitleStyle = TextStyle(
    fontSize: 22,
    fontWeight: FontWeight.w600,
    color: AppConstants.movistarGray,
    letterSpacing: 0.3,
  );

  static TextStyle bodyStyle = TextStyle(
    fontSize: 16,
    color: Colors.grey.shade700,
    height: 1.5,
  );

  // Estilos de botones modernos
  static ButtonStyle primaryButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: AppConstants.movistarBlue,
    foregroundColor: Colors.white,
    elevation: 3,
    shadowColor: AppConstants.movistarBlue.withOpacity(0.3),
    padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 18),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12),
    ),
    textStyle: const TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w600,
      letterSpacing: 0.5,
    ),
  );

  static ButtonStyle secondaryButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: AppConstants.movistarGreen,
    foregroundColor: Colors.white,
    elevation: 3,
    shadowColor: AppConstants.movistarGreen.withOpacity(0.3),
    padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 18),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12),
    ),
    textStyle: const TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w600,
      letterSpacing: 0.5,
    ),
  );

  // Estilo de botón outline moderno
  static ButtonStyle outlineButtonStyle = OutlinedButton.styleFrom(
    foregroundColor: AppConstants.movistarBlue,
    side: const BorderSide(color: AppConstants.movistarBlue, width: 2),
    padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 18),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12),
    ),
    textStyle: const TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w600,
      letterSpacing: 0.5,
    ),
  );

  // Decoración de input moderna
  static InputDecoration inputDecoration(String label) {
    return InputDecoration(
      labelText: label,
      labelStyle: TextStyle(color: Colors.grey.shade600, fontSize: 16),
      filled: true,
      fillColor: Colors.white,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: Colors.grey.shade300),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: Colors.grey.shade300),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: AppConstants.movistarBlue, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: AppConstants.movistarRed, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
    );
  }

  // Estilos de contenedores modernos
  static BoxDecoration cardDecoration = BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.circular(16),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withOpacity(0.08),
        blurRadius: 10,
        offset: const Offset(0, 4),
      ),
    ],
  );

  static BoxDecoration gradientDecoration = BoxDecoration(
    gradient: LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        AppConstants.movistarBlue.withOpacity(0.05),
        Colors.white,
      ],
    ),
  );
}
