import 'package:flutter/material.dart';

class AppConstants {
  // Colores de Movistar
  static const Color movistarBlue = Color(0xFF0066CC);
  static const Color movistarLightBlue = Color(0xFF4A90E2);
  static const Color movistarGreen = Color(0xFF00A651);
  static const Color movistarRed = Color(0xFFE53E3E);
  static const Color movistarGray = Color(0xFF666666);

  // Textos de la aplicación
  static const String appTitle = 'Auditoría Movistar';
  static const String nuevaAuditoria = 'Nueva Auditoría';
  static const String exportarExcel = 'Exportar Excel';
  static const String datosGenerales = 'Datos Generales';
  static const String capturarFotos = 'Capturar Fotos';
  static const String generarReporte = 'Generar Reporte';

  // Tipos de solicitud
  static const List<String> tiposSolicitud = [
    'Instalación',
    'Reparación',
    'Mantenimiento',
    'Migración',
    'Retiro'
  ];

  // Empresas contratistas comunes
  static const List<String> empresasContratistas = [
    'Cobra',
    'Axtel',
    'Alestra',
    'Megacable',
    'Totalplay',
    'Otra'
  ];

  // Mensajes de validación
  static const String campoRequerido = 'Este campo es requerido';
  static const String fotoRequerida = 'Debe tomar al menos una foto';
  static const String auditoriaIncompleta = 'Complete todos los campos y fotos antes de exportar';
  static const String auditoriaExportada = 'Auditoría exportada exitosamente';
  static const String errorExportacion = 'Error al exportar la auditoría';

  // Configuración de fotos
  static const double maxImageWidth = 800;
  static const double maxImageHeight = 600;
  static const int imageQuality = 85;

  // Rutas de archivos
  static const String carpetaFotos = 'auditorias_fotos';
  static const String carpetaExcel = 'auditorias_excel';
  static const String prefijoArchivo = 'auditoria_movistar_';
}

class AppStyles {
  static const TextStyle titleStyle = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppConstants.movistarBlue,
  );

  static const TextStyle subtitleStyle = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: AppConstants.movistarGray,
  );

  static const TextStyle bodyStyle = TextStyle(
    fontSize: 16,
    color: AppConstants.movistarGray,
  );

  static ButtonStyle primaryButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: AppConstants.movistarBlue,
    foregroundColor: Colors.white,
    padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(8),
    ),
  );

  static ButtonStyle secondaryButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: AppConstants.movistarGreen,
    foregroundColor: Colors.white,
    padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(8),
    ),
  );

  static InputDecoration inputDecoration(String label) {
    return InputDecoration(
      labelText: label,
      labelStyle: const TextStyle(color: AppConstants.movistarGray),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppConstants.movistarBlue),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: AppConstants.movistarBlue, width: 2),
      ),
    );
  }
}
