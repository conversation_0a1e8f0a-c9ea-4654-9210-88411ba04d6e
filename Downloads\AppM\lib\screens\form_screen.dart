import 'package:flutter/material.dart';
import '../models/auditoria_model.dart';
import '../utils/constants.dart';
import 'capture_screen.dart';

class FormScreen extends StatefulWidget {
  final AuditoriaModel auditoria;

  const FormScreen({super.key, required this.auditoria});

  @override
  State<FormScreen> createState() => _FormScreenState();
}

class _FormScreenState extends State<FormScreen> {
  final _formKey = GlobalKey<FormState>();
  
  // Controladores para los campos de texto
  late TextEditingController _numeroOrdenController;
  late TextEditingController _nombreTecnicoController;
  late TextEditingController _cedulaTecnicoController;
  late TextEditingController _nombreAuditorController;
  late TextEditingController _ciudadController;
  late TextEditingController _direccionController;
  late TextEditingController _observacionesController;
  
  // Variables para dropdowns
  String? _empresaSeleccionada;
  String? _tipoSolicitudSeleccionada;
  
  @override
  void initState() {
    super.initState();
    
    // Inicializar controladores con datos existentes
    _numeroOrdenController = TextEditingController(text: widget.auditoria.numeroOrden);
    _nombreTecnicoController = TextEditingController(text: widget.auditoria.nombreTecnico);
    _cedulaTecnicoController = TextEditingController(text: widget.auditoria.cedulaTecnico);
    _nombreAuditorController = TextEditingController(text: widget.auditoria.nombreAuditor);
    _ciudadController = TextEditingController(text: widget.auditoria.ciudad);
    _direccionController = TextEditingController(text: widget.auditoria.direccion);
    _observacionesController = TextEditingController(text: widget.auditoria.observaciones);
    
    // Inicializar dropdowns
    _empresaSeleccionada = widget.auditoria.empresaContratista.isNotEmpty 
        ? widget.auditoria.empresaContratista 
        : null;
    _tipoSolicitudSeleccionada = widget.auditoria.tipoSolicitud.isNotEmpty 
        ? widget.auditoria.tipoSolicitud 
        : null;
        
    // Establecer fecha actual si no existe
    if (widget.auditoria.fechaAuditoria.isEmpty) {
      widget.auditoria.fechaAuditoria = _formatearFecha(DateTime.now());
    }
  }

  @override
  void dispose() {
    _numeroOrdenController.dispose();
    _nombreTecnicoController.dispose();
    _cedulaTecnicoController.dispose();
    _nombreAuditorController.dispose();
    _ciudadController.dispose();
    _direccionController.dispose();
    _observacionesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          AppConstants.datosGenerales,
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppConstants.movistarBlue,
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppConstants.movistarBlue.withOpacity(0.1),
              Colors.white,
            ],
          ),
        ),
        child: SafeArea(
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                // Formulario scrolleable
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Información de la fecha
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.blue[50],
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: AppConstants.movistarLightBlue.withOpacity(0.3)),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.calendar_today, color: AppConstants.movistarBlue),
                              const SizedBox(width: 12),
                              Text(
                                'Fecha: ${widget.auditoria.fechaAuditoria}',
                                style: AppStyles.bodyStyle.copyWith(
                                  color: AppConstants.movistarBlue,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(height: 24),
                        
                        // Número de Orden
                        TextFormField(
                          controller: _numeroOrdenController,
                          decoration: AppStyles.inputDecoration('Número de Orden *'),
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return AppConstants.campoRequerido;
                            }
                            return null;
                          },
                          onChanged: (value) {
                            widget.auditoria.numeroOrden = value.trim();
                          },
                        ),
                        
                        const SizedBox(height: 20),
                        
                        // Nombre del Técnico
                        TextFormField(
                          controller: _nombreTecnicoController,
                          decoration: AppStyles.inputDecoration('Nombre del Técnico *'),
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return AppConstants.campoRequerido;
                            }
                            return null;
                          },
                          onChanged: (value) {
                            widget.auditoria.nombreTecnico = value.trim();
                          },
                        ),

                        const SizedBox(height: 20),

                        // Cédula del Técnico
                        TextFormField(
                          controller: _cedulaTecnicoController,
                          decoration: AppStyles.inputDecoration('Cédula del Técnico *'),
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return AppConstants.campoRequerido;
                            }
                            return null;
                          },
                          onChanged: (value) {
                            widget.auditoria.cedulaTecnico = value.trim();
                          },
                        ),

                        const SizedBox(height: 20),

                        // Nombre del Auditor
                        TextFormField(
                          controller: _nombreAuditorController,
                          decoration: AppStyles.inputDecoration('Nombre del Auditor *'),
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return AppConstants.campoRequerido;
                            }
                            return null;
                          },
                          onChanged: (value) {
                            widget.auditoria.nombreAuditor = value.trim();
                          },
                        ),

                        const SizedBox(height: 20),

                        // Ciudad
                        TextFormField(
                          controller: _ciudadController,
                          decoration: AppStyles.inputDecoration('Ciudad *'),
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return AppConstants.campoRequerido;
                            }
                            return null;
                          },
                          onChanged: (value) {
                            widget.auditoria.ciudad = value.trim();
                          },
                        ),

                        const SizedBox(height: 20),
                        
                        // Empresa Contratista
                        DropdownButtonFormField<String>(
                          value: _empresaSeleccionada,
                          decoration: AppStyles.inputDecoration('Empresa Contratista *'),
                          items: AppConstants.empresasContratistas.map((empresa) {
                            return DropdownMenuItem(
                              value: empresa,
                              child: Text(empresa),
                            );
                          }).toList(),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return AppConstants.campoRequerido;
                            }
                            return null;
                          },
                          onChanged: (value) {
                            setState(() {
                              _empresaSeleccionada = value;
                              widget.auditoria.empresaContratista = value ?? '';
                            });
                          },
                        ),
                        
                        const SizedBox(height: 20),
                        
                        // Tipo de Solicitud
                        DropdownButtonFormField<String>(
                          value: _tipoSolicitudSeleccionada,
                          decoration: AppStyles.inputDecoration('Tipo de Solicitud *'),
                          items: AppConstants.tiposSolicitud.map((tipo) {
                            return DropdownMenuItem(
                              value: tipo,
                              child: Text(tipo),
                            );
                          }).toList(),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return AppConstants.campoRequerido;
                            }
                            return null;
                          },
                          onChanged: (value) {
                            setState(() {
                              _tipoSolicitudSeleccionada = value;
                              widget.auditoria.tipoSolicitud = value ?? '';
                            });
                          },
                        ),
                        
                        const SizedBox(height: 20),
                        
                        // Dirección
                        TextFormField(
                          controller: _direccionController,
                          decoration: AppStyles.inputDecoration('Dirección'),
                          maxLines: 2,
                          onChanged: (value) {
                            widget.auditoria.direccion = value.trim();
                          },
                        ),
                        
                        const SizedBox(height: 20),
                        
                        // Observaciones
                        TextFormField(
                          controller: _observacionesController,
                          decoration: AppStyles.inputDecoration('Observaciones'),
                          maxLines: 3,
                          onChanged: (value) {
                            widget.auditoria.observaciones = value.trim();
                          },
                        ),
                        
                        const SizedBox(height: 40),
                        
                        // Información de ítems
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.green[50],
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: AppConstants.movistarGreen.withOpacity(0.3)),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(Icons.camera_alt_outlined, color: AppConstants.movistarGreen),
                                  const SizedBox(width: 12),
                                  Text(
                                    'Siguiente: Captura de Fotos',
                                    style: AppStyles.subtitleStyle.copyWith(
                                      color: AppConstants.movistarGreen,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Se tomarán fotos para ${AuditoriaModel.itemsAuditoria.length} ítems diferentes',
                                style: AppStyles.bodyStyle.copyWith(fontSize: 14),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                
                // Botón continuar
                Container(
                  padding: const EdgeInsets.all(24.0),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, -5),
                      ),
                    ],
                  ),
                  child: SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: ElevatedButton.icon(
                      onPressed: _continuarACaptura,
                      icon: const Icon(Icons.camera_alt_outlined, size: 24),
                      label: const Text(
                        AppConstants.capturarFotos,
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                      ),
                      style: AppStyles.primaryButtonStyle,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Continuar a la pantalla de captura de fotos
  void _continuarACaptura() {
    if (_formKey.currentState!.validate()) {
      // Guardar datos del formulario
      _formKey.currentState!.save();
      
      // Navegar a la pantalla de captura
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CaptureScreen(auditoria: widget.auditoria),
        ),
      );
    }
  }

  // Formatear fecha
  String _formatearFecha(DateTime fecha) {
    return '${fecha.day.toString().padLeft(2, '0')}/'
           '${fecha.month.toString().padLeft(2, '0')}/'
           '${fecha.year}';
  }
}
